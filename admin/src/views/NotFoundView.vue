<template>
  <div class="not-found">
    <div class="not-found-content">
      <el-icon class="not-found-icon"><Warning /></el-icon>
      <h1>404</h1>
      <p>页面不存在</p>
      <el-button type="primary" @click="goHome">返回首页</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { Warning } from '@element-plus/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/')
}
</script>

<style scoped>
.not-found {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

.not-found-content {
  text-align: center;
}

.not-found-icon {
  font-size: 64px;
  color: #e6a23c;
  margin-bottom: 20px;
}

h1 {
  font-size: 72px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
}

p {
  font-size: 18px;
  color: #606266;
  margin: 0 0 32px 0;
}
</style>
