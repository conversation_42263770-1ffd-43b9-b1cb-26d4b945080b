import { defineStore } from 'pinia'
import { ref } from 'vue'
import { request } from '@/utils/request'

export interface OverviewData {
  today: {
    newUsers: number
    orders: number
    sales: number
  }
  yesterday: {
    newUsers: number
    orders: number
    sales: number
  }
  total: {
    users: number
    orders: number
    sales: number
    products: number
  }
  products: {
    total: number
    active: number
    lowStock: number
  }
}

export interface PendingTasks {
  pendingOrders: {
    pendingPayment: number
    processing: number
    shipped: number
    total: number
  }
  lowStock: {
    count: number
    products: Array<{
      id: number
      name: string
      sku: string
      stock_quantity: number
      main_image_url: string
      category_name: string
    }>
  }
}

export interface SalesTrendItem {
  date: string
  orderCount: number
  salesAmount: number
}

export interface QuickAction {
  id: string
  title: string
  description: string
  icon: string
  route: string
  permission: string
}

export const useDashboardStore = defineStore('dashboard', () => {
  // 状态
  const overview = ref<OverviewData | null>(null)
  const pendingTasks = ref<PendingTasks | null>(null)
  const salesTrend = ref<SalesTrendItem[]>([])
  const quickActions = ref<QuickAction[]>([])
  const loading = ref(false)

  // 获取概览数据
  const fetchOverview = async () => {
    loading.value = true
    try {
      const response = await request.get<{
        success: boolean
        data: OverviewData
      }>('/dashboard/overview')
      
      if (response.success) {
        overview.value = response.data
      }
    } catch (error) {
      console.error('获取概览数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 获取待办事项
  const fetchPendingTasks = async () => {
    try {
      const response = await request.get<{
        success: boolean
        data: PendingTasks
      }>('/dashboard/pending-tasks')
      
      if (response.success) {
        pendingTasks.value = response.data
      }
    } catch (error) {
      console.error('获取待办事项失败:', error)
    }
  }

  // 获取销售趋势
  const fetchSalesTrend = async (days: number = 7) => {
    try {
      const response = await request.get<{
        success: boolean
        data: SalesTrendItem[]
      }>(`/dashboard/sales-trend?days=${days}`)
      
      if (response.success) {
        salesTrend.value = response.data
      }
    } catch (error) {
      console.error('获取销售趋势失败:', error)
    }
  }

  // 获取快捷操作
  const fetchQuickActions = async () => {
    try {
      const response = await request.get<{
        success: boolean
        data: QuickAction[]
      }>('/dashboard/quick-actions')
      
      if (response.success) {
        quickActions.value = response.data
      }
    } catch (error) {
      console.error('获取快捷操作失败:', error)
    }
  }

  // 刷新所有数据
  const refreshAll = async () => {
    await Promise.all([
      fetchOverview(),
      fetchPendingTasks(),
      fetchSalesTrend(),
      fetchQuickActions()
    ])
  }

  return {
    // 状态
    overview,
    pendingTasks,
    salesTrend,
    quickActions,
    loading,
    
    // 方法
    fetchOverview,
    fetchPendingTasks,
    fetchSalesTrend,
    fetchQuickActions,
    refreshAll
  }
})

export default useDashboardStore
