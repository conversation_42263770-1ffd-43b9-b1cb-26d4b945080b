/**
 * 统一响应格式中间件
 * 为res对象添加统一的响应方法
 */
const responseMiddleware = (req, res, next) => {
  /**
   * 成功响应
   * @param {*} data - 响应数据
   * @param {string} message - 响应消息
   * @param {number} code - 状态码
   */
  res.success = (data = null, message = '操作成功', code = 200) => {
    res.status(code).json({
      success: true,
      message,
      data,
      timestamp: new Date().toISOString()
    });
  };

  /**
   * 失败响应
   * @param {string} message - 错误消息
   * @param {number} code - 状态码
   * @param {*} error - 错误详情
   */
  res.error = (message = '操作失败', code = 400, error = null) => {
    const response = {
      success: false,
      message,
      timestamp: new Date().toISOString()
    };

    if (error && process.env.NODE_ENV === 'development') {
      response.error = error;
    }

    res.status(code).json(response);
  };

  /**
   * 分页响应
   * @param {Array} data - 数据列表
   * @param {number} total - 总数
   * @param {number} page - 当前页
   * @param {number} pageSize - 每页大小
   * @param {string} message - 响应消息
   */
  res.paginate = (data, total, page, pageSize, message = '获取成功') => {
    res.status(200).json({
      success: true,
      message,
      data,
      pagination: {
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(total / pageSize),
        hasNext: page * pageSize < total,
        hasPrev: page > 1
      },
      timestamp: new Date().toISOString()
    });
  };

  next();
};

module.exports = responseMiddleware;
