const express = require('express');
const { authenticateToken, requirePermissions } = require('../middleware/auth');
const Category = require('../models/Category');

const router = express.Router();

// 所有分类路由都需要认证
router.use(authenticateToken);

/**
 * 获取分类树
 * GET /api/categories/tree
 */
router.get('/tree', requirePermissions(['category:view']), async (req, res) => {
  try {
    const tree = await Category.getTree();
    res.success(tree);

  } catch (error) {
    console.error('获取分类树失败:', error);
    res.error('获取分类树失败', 500, error.message);
  }
});

/**
 * 获取分类列表
 * GET /api/categories
 */
router.get('/', requirePermissions(['category:view']), async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 10,
      search = '',
      parent_id = null
    } = req.query;

    const result = await Category.getList({
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      search,
      parent_id: parent_id !== null ? parseInt(parent_id) : null
    });

    res.paginate(result.categories, result.total, page, pageSize);

  } catch (error) {
    console.error('获取分类列表失败:', error);
    res.error('获取分类列表失败', 500, error.message);
  }
});

/**
 * 获取分类详情
 * GET /api/categories/:id
 */
router.get('/:id', requirePermissions(['category:view']), async (req, res) => {
  try {
    const { id } = req.params;

    const category = await Category.findById(id);
    if (!category) {
      return res.error('分类不存在', 404);
    }

    res.success(category);

  } catch (error) {
    console.error('获取分类详情失败:', error);
    res.error('获取分类详情失败', 500, error.message);
  }
});

/**
 * 创建分类
 * POST /api/categories
 */
router.post('/', requirePermissions(['category:create']), async (req, res) => {
  try {
    const { parent_id, name, icon_url, sort_order } = req.body;

    // 验证必填字段
    if (!name || name.trim() === '') {
      return res.error('分类名称不能为空', 400);
    }

    // 检查同级分类名称是否重复
    const existingCategory = await Category.findByNameAndParent(name.trim(), parent_id || 0);
    if (existingCategory) {
      return res.error('同级分类中已存在相同名称', 400);
    }

    const category = await Category.create({
      parent_id: parent_id || 0,
      name: name.trim(),
      icon_url: icon_url || null,
      sort_order: sort_order || 99
    });

    res.success(category, '创建成功');

  } catch (error) {
    console.error('创建分类失败:', error);
    res.error('创建分类失败', 500, error.message);
  }
});

/**
 * 更新分类
 * PUT /api/categories/:id
 */
router.put('/:id', requirePermissions(['category:edit']), async (req, res) => {
  try {
    const { id } = req.params;
    const { parent_id, name, icon_url, sort_order } = req.body;

    // 检查分类是否存在
    const existingCategory = await Category.findById(id);
    if (!existingCategory) {
      return res.error('分类不存在', 404);
    }

    // 验证必填字段
    if (!name || name.trim() === '') {
      return res.error('分类名称不能为空', 400);
    }

    // 检查是否设置自己为父分类
    if (parent_id && parseInt(parent_id) === parseInt(id)) {
      return res.error('不能将自己设置为父分类', 400);
    }

    // 检查同级分类名称是否重复（排除自己）
    const duplicateCategory = await Category.findByNameAndParent(name.trim(), parent_id || 0);
    if (duplicateCategory && duplicateCategory.id !== parseInt(id)) {
      return res.error('同级分类中已存在相同名称', 400);
    }

    const category = await Category.update(id, {
      parent_id: parent_id || 0,
      name: name.trim(),
      icon_url: icon_url || null,
      sort_order: sort_order || 99
    });

    res.success(category, '更新成功');

  } catch (error) {
    console.error('更新分类失败:', error);
    res.error('更新分类失败', 500, error.message);
  }
});

/**
 * 删除分类
 * DELETE /api/categories/:id
 */
router.delete('/:id', requirePermissions(['category:delete']), async (req, res) => {
  try {
    const { id } = req.params;

    // 检查分类是否存在
    const category = await Category.findById(id);
    if (!category) {
      return res.error('分类不存在', 404);
    }

    await Category.delete(id);
    res.success(null, '删除成功');

  } catch (error) {
    console.error('删除分类失败:', error);
    res.error(error.message || '删除分类失败', 500);
  }
});

module.exports = router;
