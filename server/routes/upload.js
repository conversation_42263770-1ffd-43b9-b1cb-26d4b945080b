const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const { upload, handleUploadError, getFileUrl } = require('../middleware/upload');

const router = express.Router();

// 所有上传路由都需要认证
router.use(authenticateToken);

/**
 * 上传单个图片
 * POST /api/upload/image
 */
router.post('/image', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.error('请选择要上传的文件', 400);
    }

    // 根据文件存储路径确定子目录
    const pathParts = req.file.path.split('/');
    const subDir = pathParts[pathParts.length - 2]; // 获取倒数第二个路径部分作为子目录
    
    const fileUrl = getFileUrl(req, req.file.filename, subDir);

    res.success({
      url: fileUrl,
      filename: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size,
      mimetype: req.file.mimetype
    }, '上传成功');

  } catch (error) {
    console.error('上传文件失败:', error);
    res.error('上传失败', 500, error.message);
  }
});

/**
 * 上传多个图片
 * POST /api/upload/images
 */
router.post('/images', upload.array('files', 10), (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.error('请选择要上传的文件', 400);
    }

    const uploadedFiles = req.files.map(file => {
      // 根据文件存储路径确定子目录
      const pathParts = file.path.split('/');
      const subDir = pathParts[pathParts.length - 2];
      
      return {
        url: getFileUrl(req, file.filename, subDir),
        filename: file.filename,
        originalName: file.originalname,
        size: file.size,
        mimetype: file.mimetype
      };
    });

    res.success(uploadedFiles, '上传成功');

  } catch (error) {
    console.error('上传文件失败:', error);
    res.error('上传失败', 500, error.message);
  }
});

/**
 * 上传商品图片
 * POST /api/upload/product-images
 */
router.post('/product-images', upload.array('productImages', 10), (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.error('请选择要上传的文件', 400);
    }

    const uploadedFiles = req.files.map(file => {
      return {
        url: getFileUrl(req, file.filename, 'products'),
        filename: file.filename,
        originalName: file.originalname,
        size: file.size,
        mimetype: file.mimetype
      };
    });

    res.success(uploadedFiles, '上传成功');

  } catch (error) {
    console.error('上传商品图片失败:', error);
    res.error('上传失败', 500, error.message);
  }
});

/**
 * 上传分类图标
 * POST /api/upload/category-icon
 */
router.post('/category-icon', upload.single('categoryIcon'), (req, res) => {
  try {
    if (!req.file) {
      return res.error('请选择要上传的文件', 400);
    }

    const fileUrl = getFileUrl(req, req.file.filename, 'categories');

    res.success({
      url: fileUrl,
      filename: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size,
      mimetype: req.file.mimetype
    }, '上传成功');

  } catch (error) {
    console.error('上传分类图标失败:', error);
    res.error('上传失败', 500, error.message);
  }
});

// 错误处理中间件
router.use(handleUploadError);

module.exports = router;
