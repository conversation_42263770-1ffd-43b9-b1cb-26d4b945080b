const { query } = require('../config/db');
const bcrypt = require('bcryptjs');

class AdminUser {
  /**
   * 根据用户名查找管理员
   * @param {string} username - 用户名
   * @returns {Promise<Object|null>} 管理员信息
   */
  static async findByUsername(username) {
    try {
      const users = await query(`
        SELECT au.*, ar.role_name, ar.permissions 
        FROM admin_users au 
        LEFT JOIN admin_roles ar ON au.role_id = ar.id 
        WHERE au.username = ?
      `, [username]);
      
      if (users.length === 0) return null;
      
      const user = users[0];
      // 处理权限字段
      if (user.permissions) {
        try {
          // MySQL的JSON字段可能直接返回对象/数组，也可能返回字符串
          if (Array.isArray(user.permissions)) {
            // 如果已经是数组，直接使用（无需操作）
          } else if (typeof user.permissions === 'string') {
            // 如果是字符串，尝试解析
            if (user.permissions === '*') {
              user.permissions = ['*'];
            } else {
              user.permissions = JSON.parse(user.permissions);
            }
          } else {
            // 其他情况，设为空数组
            console.warn('未知的权限数据类型:', typeof user.permissions, user.permissions);
            user.permissions = [];
          }
        } catch (error) {
          console.error('权限解析错误:', error, '原始数据:', user.permissions, '数据类型:', typeof user.permissions);
          user.permissions = [];
        }
      } else {
        user.permissions = [];
      }
      
      return user;
    } catch (error) {
      console.error('查找管理员失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID查找管理员
   * @param {number} id - 管理员ID
   * @returns {Promise<Object|null>} 管理员信息
   */
  static async findById(id) {
    try {
      const users = await query(`
        SELECT au.*, ar.role_name, ar.permissions 
        FROM admin_users au 
        LEFT JOIN admin_roles ar ON au.role_id = ar.id 
        WHERE au.id = ?
      `, [id]);
      
      if (users.length === 0) return null;
      
      const user = users[0];
      // 处理权限字段
      if (user.permissions) {
        try {
          // MySQL的JSON字段可能直接返回对象/数组，也可能返回字符串
          if (Array.isArray(user.permissions)) {
            // 如果已经是数组，直接使用（无需操作）
          } else if (typeof user.permissions === 'string') {
            // 如果是字符串，尝试解析
            if (user.permissions === '*') {
              user.permissions = ['*'];
            } else {
              user.permissions = JSON.parse(user.permissions);
            }
          } else {
            // 其他情况，设为空数组
            console.warn('未知的权限数据类型:', typeof user.permissions, user.permissions);
            user.permissions = [];
          }
        } catch (error) {
          console.error('权限解析错误:', error, '原始数据:', user.permissions, '数据类型:', typeof user.permissions);
          user.permissions = [];
        }
      } else {
        user.permissions = [];
      }
      
      return user;
    } catch (error) {
      console.error('查找管理员失败:', error);
      throw error;
    }
  }

  /**
   * 创建管理员
   * @param {Object} userData - 管理员数据
   * @returns {Promise<Object>} 创建的管理员信息
   */
  static async create(userData) {
    try {
      const { username, password, full_name, role_id } = userData;
      
      // 检查用户名是否已存在
      const existingUser = await this.findByUsername(username);
      if (existingUser) {
        throw new Error('用户名已存在');
      }

      // 加密密码
      const saltRounds = 10;
      const password_hash = await bcrypt.hash(password, saltRounds);

      const result = await query(
        'INSERT INTO admin_users (username, password_hash, full_name, role_id) VALUES (?, ?, ?, ?)',
        [username, password_hash, full_name, role_id]
      );

      return await this.findById(result.insertId);
    } catch (error) {
      console.error('创建管理员失败:', error);
      throw error;
    }
  }

  /**
   * 验证密码
   * @param {string} password - 明文密码
   * @param {string} hash - 密码哈希
   * @returns {Promise<boolean>} 验证结果
   */
  static async verifyPassword(password, hash) {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      console.error('密码验证失败:', error);
      return false;
    }
  }

  /**
   * 更新管理员信息
   * @param {number} id - 管理员ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 更新后的管理员信息
   */
  static async update(id, updateData) {
    try {
      const fields = [];
      const values = [];

      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined) {
          if (key === 'password') {
            // 如果更新密码，需要加密
            fields.push('password_hash = ?');
            values.push(bcrypt.hashSync(updateData[key], 10));
          } else {
            fields.push(`${key} = ?`);
            values.push(updateData[key]);
          }
        }
      });

      if (fields.length === 0) {
        throw new Error('没有要更新的字段');
      }

      values.push(id);
      await query(
        `UPDATE admin_users SET ${fields.join(', ')} WHERE id = ?`,
        values
      );

      return await this.findById(id);
    } catch (error) {
      console.error('更新管理员失败:', error);
      throw error;
    }
  }

  /**
   * 更新最后登录时间
   * @param {number} id - 管理员ID
   * @returns {Promise<void>}
   */
  static async updateLastLogin(id) {
    try {
      await query(
        'UPDATE admin_users SET last_login_at = NOW() WHERE id = ?',
        [id]
      );
    } catch (error) {
      console.error('更新登录时间失败:', error);
      throw error;
    }
  }

  /**
   * 获取管理员列表（分页）
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 管理员列表和总数
   */
  static async getList(options = {}) {
    try {
      const {
        page = 1,
        pageSize = 10,
        search = '',
        role_id = null,
        is_active = null
      } = options;

      const offset = (page - 1) * pageSize;
      let whereClause = 'WHERE 1=1';
      let params = [];

      if (search) {
        whereClause += ' AND (au.username LIKE ? OR au.full_name LIKE ?)';
        params.push(`%${search}%`, `%${search}%`);
      }

      if (role_id) {
        whereClause += ' AND au.role_id = ?';
        params.push(role_id);
      }

      if (is_active !== null) {
        whereClause += ' AND au.is_active = ?';
        params.push(is_active);
      }

      // 获取总数
      const countResult = await query(`
        SELECT COUNT(*) as total 
        FROM admin_users au 
        LEFT JOIN admin_roles ar ON au.role_id = ar.id 
        ${whereClause}
      `, params);
      const total = countResult[0].total;

      // 获取列表
      const users = await query(`
        SELECT 
          au.id, au.username, au.full_name, au.is_active, au.last_login_at, au.created_at,
          ar.role_name
        FROM admin_users au 
        LEFT JOIN admin_roles ar ON au.role_id = ar.id 
        ${whereClause}
        ORDER BY au.created_at DESC
        LIMIT ? OFFSET ?
      `, [...params, pageSize, offset]);

      return { users, total };
    } catch (error) {
      console.error('获取管理员列表失败:', error);
      throw error;
    }
  }

  /**
   * 删除管理员
   * @param {number} id - 管理员ID
   * @returns {Promise<boolean>} 删除结果
   */
  static async delete(id) {
    try {
      const result = await query('DELETE FROM admin_users WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除管理员失败:', error);
      throw error;
    }
  }

  /**
   * 启用/禁用管理员
   * @param {number} id - 管理员ID
   * @param {boolean} isActive - 是否启用
   * @returns {Promise<Object>} 更新后的管理员信息
   */
  static async toggleActive(id, isActive) {
    try {
      await query(
        'UPDATE admin_users SET is_active = ? WHERE id = ?',
        [isActive, id]
      );

      return await this.findById(id);
    } catch (error) {
      console.error('切换管理员状态失败:', error);
      throw error;
    }
  }
}

module.exports = AdminUser;
